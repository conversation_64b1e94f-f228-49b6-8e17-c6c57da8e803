#!/usr/bin/env python3
"""
简单启动Flask应用的脚本
"""
from flask import Flask, render_template, jsonify, request, send_file
import os
import sys
from video_processor import VideoProcessor

# 配置
VIDEO_DIR = "../dataset_drone_video"
ANALYSIS_FILE = "../video_prefix_analysis.txt"
BEST_VIDEO_DIR = "../Best_video"
PROMPTS_FILE = "../drone_prompts.csv"

app = Flask(__name__)

# 全局变量
processor = None

def init_processor():
    """初始化视频处理器"""
    global processor
    processor = VideoProcessor(ANALYSIS_FILE, VIDEO_DIR, BEST_VIDEO_DIR, PROMPTS_FILE)

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前评估状态"""
    try:
        status = processor.get_status()
        return jsonify({"success": True, "data": status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_progress')
def sync_progress():
    """手动触发进度同步"""
    try:
        sync_result = processor.sync_with_best_video_dir()
        
        # 获取同步后的状态
        status = processor.get_status()
        sync_status = processor.get_sync_status()
        
        return jsonify({
            "success": True, 
            "data": {
                "sync_result": sync_result,
                "updated_status": status,
                "sync_status": sync_status
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_status')
def get_sync_status():
    """获取同步状态信息"""
    try:
        sync_status = processor.get_sync_status()
        return jsonify({"success": True, "data": sync_status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    # 初始化处理器
    init_processor()
    
    print("视频质量评估工具启动中...")
    print(f"总前缀数量: {len(processor.prefix_groups)}")
    
    # 执行进度同步
    print("\n正在同步评估进度...")
    sync_result = processor.sync_with_best_video_dir()
    
    print(f"📁 扫描Best_video文件: {sync_result['scanned_files']} 个")
    print(f"✅ 恢复已完成前缀: {sync_result['found_completed']} 个")
    print(f"🗑️  移除缺失前缀: {sync_result['removed_missing']} 个")
    
    # 显示同步后的状态
    status = processor.get_status()
    sync_status = processor.get_sync_status()
    
    print(f"\n📊 同步后状态:")
    print(f"   已完成: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
    print(f"   同步状态: {'✅ 已同步' if sync_status['is_synced'] else '⚠️ 需要同步'}")
    
    print(f"\n🌐 启动Web服务器: http://127.0.0.1:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
