#!/usr/bin/env python3
"""
完整的同步功能演示脚本
"""
import os
import sys
import shutil
import time
from datetime import datetime

# 添加checker目录到路径
sys.path.append('checker')
from checker.video_processor import VideoProcessor

def print_separator(title):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_status(processor):
    """打印当前状态"""
    status = processor.get_status()
    sync_status = processor.get_sync_status()
    
    print(f"📊 总前缀数: {status['total_prefixes']}")
    print(f"✅ 已完成: {status['completed_prefixes']}")
    print(f"📈 进度: {status['progress_percentage']:.1f}%")
    print(f"📁 Best_video文件数: {sync_status['best_video_files_count']}")
    print(f"📊 状态记录数: {sync_status['state_completed_count']}")
    print(f"🔄 同步状态: {'✅ 已同步' if sync_status['is_synced'] else '❌ 不同步'}")

def create_test_best_video(processor, prefix_name):
    """创建测试用的最佳视频文件"""
    # 查找源视频文件
    source_videos = []
    for filename in os.listdir("dataset_drone_video"):
        if filename.startswith(prefix_name) and filename.endswith('.mp4'):
            source_videos.append(filename)
            break
    
    if not source_videos:
        print(f"❌ 未找到前缀 {prefix_name} 的源视频文件")
        return False
    
    source_file = source_videos[0]
    source_path = os.path.join("dataset_drone_video", source_file)
    dest_filename = f"{prefix_name}_BEST.mp4"
    dest_path = os.path.join("Best_video", dest_filename)
    
    try:
        shutil.copy2(source_path, dest_path)
        print(f"✅ 创建测试文件: {dest_filename}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def delete_best_video(prefix_name):
    """删除最佳视频文件"""
    dest_filename = f"{prefix_name}_BEST.mp4"
    dest_path = os.path.join("Best_video", dest_filename)
    
    try:
        if os.path.exists(dest_path):
            os.remove(dest_path)
            print(f"🗑️  删除文件: {dest_filename}")
            return True
        else:
            print(f"❌ 文件不存在: {dest_filename}")
            return False
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False

def main():
    """主演示函数"""
    print_separator("视频评估工具同步功能完整演示")
    
    # 1. 初始化VideoProcessor
    print("\n1. 初始化VideoProcessor...")
    try:
        processor = VideoProcessor(
            "video_prefix_analysis.txt", 
            "dataset_drone_video", 
            "Best_video", 
            "drone_prompts.csv"
        )
        print(f"   ✅ 成功加载 {len(processor.prefix_groups)} 个前缀组")
        print(f"   ✅ 成功加载 {len(processor.prompts)} 个提示信息")
    except Exception as e:
        print(f"   ❌ 初始化失败: {e}")
        return False
    
    # 2. 显示初始状态
    print_separator("2. 初始状态检查")
    print_status(processor)
    
    # 3. 执行初始同步
    print_separator("3. 执行初始同步")
    sync_result = processor.sync_with_best_video_dir()
    print(f"📁 扫描文件: {sync_result['scanned_files']} 个")
    print(f"✅ 恢复前缀: {sync_result['found_completed']} 个")
    print(f"🗑️  移除前缀: {sync_result['removed_missing']} 个")
    
    if sync_result['errors']:
        print(f"⚠️  错误: {len(sync_result['errors'])} 个")
    
    print("\n同步后状态:")
    print_status(processor)
    
    # 4. 演示添加Best_video文件的同步
    print_separator("4. 演示：添加Best_video文件")
    
    # 获取一些可用的前缀进行测试
    available_prefixes = list(processor.prefix_groups.keys())[:3]
    print(f"选择测试前缀: {available_prefixes}")
    
    for prefix in available_prefixes:
        print(f"\n添加前缀: {prefix}")
        if create_test_best_video(processor, prefix):
            # 执行同步
            sync_result = processor.sync_with_best_video_dir()
            print(f"   同步结果: 恢复 {sync_result['found_completed']} 个前缀")
            
            # 显示更新后的状态
            status = processor.get_status()
            print(f"   更新后进度: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
        
        time.sleep(1)  # 短暂延迟
    
    print("\n添加文件后的最终状态:")
    print_status(processor)
    
    # 5. 演示删除Best_video文件的反向同步
    print_separator("5. 演示：删除Best_video文件的反向同步")
    
    # 删除一些文件
    for prefix in available_prefixes[:2]:
        print(f"\n删除前缀: {prefix}")
        if delete_best_video(prefix):
            # 执行同步
            sync_result = processor.sync_with_best_video_dir()
            print(f"   同步结果: 移除 {sync_result['removed_missing']} 个前缀")
            
            # 显示更新后的状态
            status = processor.get_status()
            print(f"   更新后进度: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
        
        time.sleep(1)  # 短暂延迟
    
    print("\n删除文件后的最终状态:")
    print_status(processor)
    
    # 6. 测试API端点功能
    print_separator("6. 测试API端点功能")
    
    print("测试 get_sync_status():")
    sync_status = processor.get_sync_status()
    print(f"   返回数据: {sync_status}")
    
    print("\n测试 sync_with_best_video_dir():")
    sync_result = processor.sync_with_best_video_dir()
    print(f"   返回数据: {sync_result}")
    
    print("\n测试 get_status():")
    status = processor.get_status()
    print(f"   总前缀: {status['total_prefixes']}")
    print(f"   已完成: {status['completed_prefixes']}")
    print(f"   进度: {status['progress_percentage']:.1f}%")
    
    # 7. 总结
    print_separator("7. 功能演示总结")
    
    print("✅ 已成功演示的功能:")
    print("   1. 基于Best_video目录的进度校准")
    print("   2. Best_video文件添加时的自动状态更新")
    print("   3. Best_video文件删除时的反向同步")
    print("   4. 准确的进度计算和显示")
    print("   5. 同步状态检测和报告")
    print("   6. API端点的正常响应")
    
    print("\n🎯 同步机制特点:")
    print("   - 双向同步：文件系统 ↔ 评估状态")
    print("   - 自动检测：文件添加/删除自动更新状态")
    print("   - 数据一致性：确保状态与实际文件匹配")
    print("   - 错误处理：优雅处理各种异常情况")
    print("   - 详细日志：记录所有同步操作")
    
    print_separator("演示完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
