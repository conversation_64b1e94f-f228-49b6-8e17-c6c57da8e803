#!/usr/bin/env python3
"""
测试同步API功能的脚本
"""
import sys
import os

# 添加checker目录到路径
sys.path.append('checker')

def test_sync_api():
    """测试同步API功能"""
    print("=== 测试同步API功能 ===\n")
    
    try:
        # 导入VideoProcessor
        from checker.video_processor import VideoProcessor
        
        # 初始化处理器
        processor = VideoProcessor(
            "video_prefix_analysis.txt", 
            "dataset_drone_video", 
            "Best_video", 
            "drone_prompts.csv"
        )
        
        print(f"✅ VideoProcessor初始化成功")
        print(f"📁 前缀组数量: {len(processor.prefix_groups)}")
        print(f"📝 提示信息数量: {len(processor.prompts)}")
        
        # 测试同步状态API
        print("\n1. 测试同步状态API...")
        sync_status = processor.get_sync_status()
        print(f"   📁 Best_video文件数: {sync_status['best_video_files_count']}")
        print(f"   📊 状态记录数: {sync_status['state_completed_count']}")
        print(f"   🔄 是否同步: {'✅ 是' if sync_status['is_synced'] else '❌ 否'}")
        
        # 测试同步进度API
        print("\n2. 测试同步进度API...")
        sync_result = processor.sync_with_best_video_dir()
        print(f"   📁 扫描文件: {sync_result['scanned_files']} 个")
        print(f"   ✅ 恢复前缀: {sync_result['found_completed']} 个")
        print(f"   🗑️  移除前缀: {sync_result['removed_missing']} 个")
        
        if sync_result['errors']:
            print(f"   ⚠️  错误: {len(sync_result['errors'])} 个")
        
        # 测试状态API
        print("\n3. 测试状态API...")
        status = processor.get_status()
        print(f"   📊 总前缀: {status['total_prefixes']}")
        print(f"   ✅ 已完成: {status['completed_prefixes']}")
        print(f"   📈 进度: {status['progress_percentage']:.1f}%")
        
        # 再次检查同步状态
        print("\n4. 再次检查同步状态...")
        sync_status_after = processor.get_sync_status()
        print(f"   📁 Best_video文件数: {sync_status_after['best_video_files_count']}")
        print(f"   📊 状态记录数: {sync_status_after['state_completed_count']}")
        print(f"   🔄 是否同步: {'✅ 是' if sync_status_after['is_synced'] else '❌ 否'}")
        
        print("\n=== 所有API测试通过 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sync_api()
    sys.exit(0 if success else 1)
