#!/usr/bin/env python3
"""
最简单的Flask测试应用
"""
from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, Flask is working!"

@app.route('/api/test')
def test_api():
    return jsonify({"success": True, "message": "API is working"})

if __name__ == '__main__':
    print("Starting simple Flask test...")
    app.run(debug=False, host='127.0.0.1', port=5001, use_reloader=False)
