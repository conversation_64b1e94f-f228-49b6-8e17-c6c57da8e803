#!/usr/bin/env python3
"""
Web界面演示应用
"""
from flask import Flask, render_template, jsonify
import os
import threading
import time
from video_processor import VideoProcessor

app = Flask(__name__)

# 全局变量
processor = None
app_running = False

def init_processor():
    """初始化处理器"""
    global processor
    try:
        processor = VideoProcessor(
            "../video_prefix_analysis.txt", 
            "../dataset_drone_video", 
            "../Best_video", 
            "../drone_prompts.csv"
        )
        
        # 执行初始同步
        sync_result = processor.sync_with_best_video_dir()
        print(f"初始同步完成: 恢复{sync_result['found_completed']}个前缀")
        
        return True
    except Exception as e:
        print(f"初始化失败: {e}")
        return False

@app.route('/')
def index():
    """主页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>视频评估工具 - 同步功能演示</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #2c3e50; margin-bottom: 30px; }
            .status-card { background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 10px 0; }
            .sync-indicator { display: inline-block; padding: 5px 10px; border-radius: 15px; color: white; font-weight: bold; margin-left: 10px; }
            .synced { background: #27ae60; }
            .not-synced { background: #e74c3c; }
            .btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn:hover { background: #2980b9; }
            .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
            .progress-fill { height: 100%; background: #3498db; transition: width 0.3s ease; }
            .api-result { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎬 视频评估工具</h1>
                <h2>进度跟踪和状态同步演示</h2>
            </div>
            
            <div class="status-card">
                <h3>📊 当前状态 <span id="syncIndicator" class="sync-indicator">检查中...</span></h3>
                <div id="statusInfo">加载中...</div>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
                <div id="progressText">0%</div>
            </div>
            
            <div class="status-card">
                <h3>🔄 同步控制</h3>
                <button class="btn" onclick="syncProgress()">手动同步</button>
                <button class="btn" onclick="refreshStatus()">刷新状态</button>
                <div id="syncResult" class="api-result" style="display: none;"></div>
            </div>
            
            <div class="status-card">
                <h3>📁 Best_video目录状态</h3>
                <div id="bestVideoInfo">检查中...</div>
            </div>
            
            <div class="status-card">
                <h3>🔧 API测试</h3>
                <button class="btn" onclick="testAPI('/api/status')">测试状态API</button>
                <button class="btn" onclick="testAPI('/api/sync_status')">测试同步状态API</button>
                <button class="btn" onclick="testAPI('/api/sync_progress')">测试同步API</button>
                <div id="apiResult" class="api-result" style="display: none;"></div>
            </div>
        </div>
        
        <script>
            let statusData = {};
            
            async function refreshStatus() {
                try {
                    const response = await fetch('/api/status');
                    const result = await response.json();
                    
                    if (result.success) {
                        statusData = result.data;
                        updateStatusDisplay();
                    }
                } catch (error) {
                    console.error('获取状态失败:', error);
                }
                
                // 同时获取同步状态
                try {
                    const syncResponse = await fetch('/api/sync_status');
                    const syncResult = await syncResponse.json();
                    
                    if (syncResult.success) {
                        updateSyncDisplay(syncResult.data);
                    }
                } catch (error) {
                    console.error('获取同步状态失败:', error);
                }
            }
            
            function updateStatusDisplay() {
                const statusInfo = document.getElementById('statusInfo');
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                
                statusInfo.innerHTML = `
                    📊 总前缀数: ${statusData.total_prefixes}<br>
                    ✅ 已完成: ${statusData.completed_prefixes}<br>
                    📈 当前前缀索引: ${statusData.current_prefix_index + 1}
                `;
                
                const progress = statusData.progress_percentage || 0;
                progressFill.style.width = progress + '%';
                progressText.textContent = progress.toFixed(1) + '%';
            }
            
            function updateSyncDisplay(syncData) {
                const syncIndicator = document.getElementById('syncIndicator');
                const bestVideoInfo = document.getElementById('bestVideoInfo');
                
                if (syncData.is_synced) {
                    syncIndicator.className = 'sync-indicator synced';
                    syncIndicator.textContent = '✅ 已同步';
                } else {
                    syncIndicator.className = 'sync-indicator not-synced';
                    syncIndicator.textContent = '⚠️ 需要同步';
                }
                
                bestVideoInfo.innerHTML = `
                    📁 Best_video文件数: ${syncData.best_video_files_count}<br>
                    📊 状态记录数: ${syncData.state_completed_count}<br>
                    🔄 同步状态: ${syncData.is_synced ? '✅ 已同步' : '❌ 不同步'}
                `;
            }
            
            async function syncProgress() {
                const syncResult = document.getElementById('syncResult');
                syncResult.style.display = 'block';
                syncResult.textContent = '正在同步...';
                
                try {
                    const response = await fetch('/api/sync_progress');
                    const result = await response.json();
                    
                    if (result.success) {
                        const sync = result.data.sync_result;
                        syncResult.textContent = `同步完成！
扫描文件: ${sync.scanned_files} 个
恢复前缀: ${sync.found_completed} 个
移除前缀: ${sync.removed_missing} 个`;
                        
                        // 刷新状态
                        setTimeout(refreshStatus, 1000);
                    } else {
                        syncResult.textContent = '同步失败: ' + result.error;
                    }
                } catch (error) {
                    syncResult.textContent = '同步错误: ' + error.message;
                }
            }
            
            async function testAPI(endpoint) {
                const apiResult = document.getElementById('apiResult');
                apiResult.style.display = 'block';
                apiResult.textContent = `正在测试 ${endpoint}...`;
                
                try {
                    const response = await fetch(endpoint);
                    const result = await response.json();
                    apiResult.textContent = `${endpoint} 响应:\\n${JSON.stringify(result, null, 2)}`;
                } catch (error) {
                    apiResult.textContent = `${endpoint} 错误: ${error.message}`;
                }
            }
            
            // 页面加载时初始化
            document.addEventListener('DOMContentLoaded', function() {
                refreshStatus();
                // 每5秒自动刷新状态
                setInterval(refreshStatus, 5000);
            });
        </script>
    </body>
    </html>
    """

@app.route('/api/status')
def get_status():
    """获取状态API"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        status = processor.get_status()
        return jsonify({"success": True, "data": status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_status')
def get_sync_status():
    """获取同步状态API"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        sync_status = processor.get_sync_status()
        return jsonify({"success": True, "data": sync_status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_progress')
def sync_progress():
    """同步进度API"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        
        sync_result = processor.sync_with_best_video_dir()
        status = processor.get_status()
        sync_status = processor.get_sync_status()
        
        return jsonify({
            "success": True, 
            "data": {
                "sync_result": sync_result,
                "updated_status": status,
                "sync_status": sync_status
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

def run_app():
    """运行Flask应用"""
    global app_running
    app_running = True
    app.run(debug=False, host='127.0.0.1', port=5002, use_reloader=False)

def main():
    """主函数"""
    print("=== 视频评估工具Web演示 ===")
    
    # 初始化处理器
    if not init_processor():
        print("❌ 初始化失败")
        return False
    
    print(f"✅ 初始化成功: {len(processor.prefix_groups)} 个前缀组")
    
    # 显示当前状态
    status = processor.get_status()
    sync_status = processor.get_sync_status()
    
    print(f"📊 当前状态: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
    print(f"🔄 同步状态: {'✅ 已同步' if sync_status['is_synced'] else '⚠️ 需要同步'}")
    
    # 启动Web服务器
    print(f"\n🌐 启动Web演示: http://127.0.0.1:5002")
    print("按 Ctrl+C 停止服务器")
    
    try:
        run_app()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    
    return True

if __name__ == '__main__':
    main()
