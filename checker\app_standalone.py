#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频质量评估工具 - 独立部署版本
支持打包为可执行文件，无需Python环境
"""
from flask import Flask, render_template, jsonify, request, send_file
import os
import sys
import webbrowser
import threading
import time
from video_processor_standalone import VideoProcessor

def get_base_path():
    """获取应用基础路径"""
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的路径
        return os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """获取资源文件路径（用于模板和静态文件）"""
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的路径
        return os.path.join(sys._MEIPASS, relative_path)
    else:
        # 开发环境路径
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path)

# 获取基础路径
BASE_PATH = get_base_path()

# 配置文件路径（相对于可执行文件）
VIDEO_DIR = os.path.join(BASE_PATH, "dataset_drone_video")
ANALYSIS_FILE = os.path.join(BASE_PATH, "video_prefix_analysis.txt")
BEST_VIDEO_DIR = os.path.join(BASE_PATH, "Best_video")
PROMPTS_FILE = os.path.join(BASE_PATH, "drone_prompts.csv")

# 创建Flask应用，指定模板和静态文件路径
template_folder = get_resource_path('templates')
static_folder = get_resource_path('static')
app = Flask(__name__, template_folder=template_folder, static_folder=static_folder)

# 全局变量
processor = None

def check_required_files():
    """检查必要文件是否存在"""
    required_files = [
        (ANALYSIS_FILE, "视频前缀分析文件"),
        (PROMPTS_FILE, "提示文件")
    ]
    
    required_dirs = [
        (VIDEO_DIR, "视频文件目录")
    ]
    
    missing_items = []
    
    # 检查文件
    for file_path, description in required_files:
        if not os.path.exists(file_path):
            missing_items.append(f"❌ 缺少{description}: {os.path.basename(file_path)}")
    
    # 检查目录
    for dir_path, description in required_dirs:
        if not os.path.exists(dir_path):
            missing_items.append(f"❌ 缺少{description}: {os.path.basename(dir_path)}")
        elif not os.listdir(dir_path):
            missing_items.append(f"⚠️  {description}为空: {os.path.basename(dir_path)}")
    
    return missing_items

def init_processor():
    """初始化视频处理器"""
    global processor
    try:
        processor = VideoProcessor(ANALYSIS_FILE, VIDEO_DIR, BEST_VIDEO_DIR, PROMPTS_FILE)
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        status = processor.get_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/next_round')
def get_next_round():
    """获取下一轮比较的视频"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        round_data = processor.get_next_round()
        return jsonify(round_data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/select_winner', methods=['POST'])
def select_winner():
    """选择获胜视频"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        data = request.get_json()
        winner = data.get('winner')
        
        if not winner:
            return jsonify({"error": "未指定获胜视频"}), 400
        
        result = processor.select_winner(winner)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/skip_prefix', methods=['POST'])
def skip_prefix():
    """跳过当前前缀"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        result = processor.skip_current_prefix()
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/reset_all')
def reset_all_evaluation():
    """重置所有评估状态"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        # 删除状态文件
        if os.path.exists(processor.state_file):
            os.remove(processor.state_file)
        
        # 重新初始化处理器
        init_processor()
        
        return jsonify({"success": True, "message": "所有评估状态已重置"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_progress')
def sync_progress():
    """手动同步进度"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        sync_result = processor.sync_with_best_video_dir()
        return jsonify({
            "success": True,
            "message": "进度同步完成",
            "sync_result": sync_result
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/export_results')
def export_results():
    """导出评估结果"""
    if not processor:
        return jsonify({"error": "处理器未初始化"}), 500
    
    try:
        report_data = processor.generate_report()
        
        # 创建临时文件
        import tempfile
        import json
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        return send_file(temp_file, as_attachment=True, download_name='evaluation_results.json')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/video/<path:filename>')
def serve_video(filename):
    """提供视频文件服务"""
    try:
        # 安全检查：确保文件名不包含路径遍历
        if '..' in filename or filename.startswith('/'):
            return "Invalid filename", 400
        
        video_path = os.path.join(VIDEO_DIR, filename)
        
        if not os.path.exists(video_path):
            return "Video not found", 404
        
        return send_file(video_path)
    except Exception as e:
        return f"Error serving video: {str(e)}", 500

def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 视频质量评估工具 - 独立版本")
    print("=" * 60)
    print()
    
    # 设置控制台编码
    if sys.platform.startswith('win'):
        os.system('chcp 65001 > nul')
    
    # 检查必要文件
    print("📋 检查运行环境...")
    missing_items = check_required_files()
    
    if missing_items:
        print("❌ 环境检查失败:")
        for item in missing_items:
            print(f"   {item}")
        print()
        print("📝 解决方案:")
        print("   1. 确保以下文件与程序在同一目录:")
        print("      - video_prefix_analysis.txt")
        print("      - drone_prompts.csv")
        print("      - dataset_drone_video/ (包含视频文件)")
        print("   2. 如果是首次使用，请参考README.txt")
        print()
        input("按回车键退出...")
        return
    
    print("✅ 环境检查通过")
    
    # 创建必要目录
    os.makedirs(BEST_VIDEO_DIR, exist_ok=True)
    
    # 初始化处理器
    print("🔧 初始化处理器...")
    if not init_processor():
        print("❌ 初始化失败，请检查数据文件格式")
        input("按回车键退出...")
        return
    
    print("✅ 处理器初始化成功")
    
    # 显示统计信息
    try:
        status = processor.get_status()
        print(f"📊 数据统计:")
        print(f"   总前缀数量: {status['total_prefixes']}")
        print(f"   已完成: {status['completed_prefixes']}")
        print(f"   进度: {status['progress_percentage']:.1f}%")
    except Exception as e:
        print(f"⚠️  获取统计信息失败: {e}")
    
    # 执行进度同步
    print("🔄 同步评估进度...")
    try:
        sync_result = processor.sync_with_best_video_dir()
        print(f"   恢复已完成前缀: {sync_result['found_completed']} 个")
        if sync_result['errors']:
            print(f"   同步警告: {len(sync_result['errors'])} 个")
    except Exception as e:
        print(f"⚠️  同步过程出现错误: {e}")
    
    print()
    print("🌐 启动Web服务器...")
    print("   访问地址: http://127.0.0.1:5000")
    print("   按 Ctrl+C 停止服务器")
    print("=" * 60)
    print()
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
