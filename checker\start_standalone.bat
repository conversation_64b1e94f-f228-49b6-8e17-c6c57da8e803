@echo off
chcp 65001 > nul
title 视频质量评估工具

echo ========================================
echo 🎬 视频质量评估工具 - 独立版本
echo ========================================
echo.

REM 检查必要文件
echo 📋 检查运行环境...

if not exist "drone_prompts.csv" (
    echo ❌ 缺少文件: drone_prompts.csv
    goto :error_exit
)

if not exist "dataset_drone_video" (
    echo ❌ 缺少目录: dataset_drone_video
    goto :error_exit
)

REM 检查分析文件（可选）
if exist "video_prefix_analysis.txt" (
    echo 📄 发现分析文件: video_prefix_analysis.txt (将优先使用)
) else (
    echo 🔍 未发现分析文件，将自动扫描视频目录
)

REM 检查视频目录是否为空
dir /b "dataset_drone_video\*.mp4" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: dataset_drone_video 目录中没有找到 .mp4 文件
    echo    请确保视频文件已正确放置
    echo.
    set /p continue="是否继续启动? (y/N): "
    if /i not "%continue%"=="y" goto :error_exit
)

echo ✅ 环境检查通过

REM 创建Best_video目录
if not exist "Best_video" (
    echo 📁 创建 Best_video 目录...
    mkdir "Best_video"
)

echo.
echo 🚀 启动应用程序...
echo    访问地址: http://127.0.0.1:5000
echo    按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动应用程序
VideoEvaluator.exe

goto :end

:error_exit
echo.
echo 📝 解决方案:
echo    1. 确保以下文件与程序在同一目录:
echo       - drone_prompts.csv (必需)
echo       - dataset_drone_video/ (必需，包含 .mp4 视频文件)
echo    2. 可选文件:
echo       - video_prefix_analysis.txt (如果提供将优先使用)
echo    3. 如果是首次使用，请参考 README_STANDALONE.txt
echo.
pause
exit /b 1

:end
echo.
echo 👋 程序已退出
pause
