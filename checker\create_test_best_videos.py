#!/usr/bin/env python3
"""
创建测试用的最佳视频文件
"""
import os
import shutil

def create_test_best_videos():
    """创建一些测试用的最佳视频文件"""
    best_video_dir = "../Best_video"
    source_video_dir = "../dataset_drone_video"
    
    # 确保Best_video目录存在
    os.makedirs(best_video_dir, exist_ok=True)
    
    # 获取一些源视频文件
    source_videos = []
    if os.path.exists(source_video_dir):
        for filename in os.listdir(source_video_dir):
            if filename.endswith('.mp4'):
                source_videos.append(filename)
                if len(source_videos) >= 3:  # 只取前3个作为测试
                    break
    
    print(f"找到 {len(source_videos)} 个源视频文件")
    
    # 创建测试的最佳视频文件
    test_prefixes = []
    for i, source_video in enumerate(source_videos):
        # 从源视频文件名提取前缀
        # 例如：frame0_from_02761_NAT2021_train_0428person3_2_0_1_results_seed1_stage2.mp4
        # 提取为：frame0_from_02761_NAT2021_train_0428person3_2_0
        
        name_without_ext = source_video.replace('.mp4', '')
        if '_results_seed' in name_without_ext:
            base_name = name_without_ext.split('_results_seed')[0]
        else:
            base_name = name_without_ext
        
        # 移除最后一个数字部分来获取前缀
        parts = base_name.split('_')
        if parts and parts[-1].isdigit():
            parts = parts[:-1]
        
        prefix = '_'.join(parts)
        test_prefixes.append(prefix)
        
        # 创建最佳视频文件
        source_path = os.path.join(source_video_dir, source_video)
        dest_filename = f"{prefix}_BEST.mp4"
        dest_path = os.path.join(best_video_dir, dest_filename)
        
        try:
            shutil.copy2(source_path, dest_path)
            print(f"✅ 创建测试文件: {dest_filename}")
        except Exception as e:
            print(f"❌ 创建失败 {dest_filename}: {e}")
    
    print(f"\n创建了 {len(test_prefixes)} 个测试最佳视频文件")
    print("测试前缀:", test_prefixes)
    
    return test_prefixes

if __name__ == "__main__":
    create_test_best_videos()
