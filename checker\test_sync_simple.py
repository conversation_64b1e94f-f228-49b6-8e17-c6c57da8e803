#!/usr/bin/env python3
"""
简单的同步功能测试脚本
"""
from video_processor import VideoProcessor

def main():
    print("=== 测试同步功能 ===")
    
    # 初始化处理器
    processor = VideoProcessor(
        "../video_prefix_analysis.txt", 
        "../dataset_drone_video", 
        "../Best_video", 
        "../drone_prompts.csv"
    )
    
    print(f"前缀组数量: {len(processor.prefix_groups)}")
    
    # 获取同步前状态
    sync_status_before = processor.get_sync_status()
    print(f"同步前 - Best_video文件: {sync_status_before['best_video_files_count']}, 状态记录: {sync_status_before['state_completed_count']}")
    
    # 执行同步
    sync_result = processor.sync_with_best_video_dir()
    print(f"同步结果: 扫描{sync_result['scanned_files']}个文件, 恢复{sync_result['found_completed']}个前缀, 移除{sync_result['removed_missing']}个前缀")
    
    # 获取同步后状态
    sync_status_after = processor.get_sync_status()
    status = processor.get_status()
    print(f"同步后 - Best_video文件: {sync_status_after['best_video_files_count']}, 状态记录: {sync_status_after['state_completed_count']}")
    print(f"总体进度: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()
