🎬 视频质量评估工具 - 独立版本
========================================

这是一个独立的可执行程序，无需安装Python环境即可运行。

📋 系统要求
--------
- Windows 7/8/10/11 (64位)
- 至少 2GB 可用内存
- 支持HTML5的现代浏览器

📁 文件结构
--------
VideoEvaluator/
├── VideoEvaluator.exe          # 主程序
├── _internal/                  # 程序依赖文件（请勿删除）
├── start_standalone.bat        # 启动脚本（推荐使用）
├── README_STANDALONE.txt       # 本说明文件
├── drone_prompts.csv          # 提示文件（必需）
├── dataset_drone_video/        # 视频文件目录（必需）
├── video_prefix_analysis.txt   # 视频前缀分析文件（可选）
├── Best_video/                # 输出目录（自动创建）
├── evaluation_state.json     # 评估状态（运行时生成）
└── evaluation_log.json       # 评估日志（运行时生成）

🚀 快速开始
--------
1. 确保以下文件/目录存在：
   必需文件：
   - drone_prompts.csv (提示文件)
   - dataset_drone_video/ (包含 .mp4 视频文件的目录)

   可选文件：
   - video_prefix_analysis.txt (如果提供将优先使用，否则自动扫描)

2. 双击运行 start_standalone.bat 启动程序
   或者直接双击 VideoEvaluator.exe

3. 程序启动后会：
   - 自动扫描视频目录并分析前缀分组
   - 自动打开浏览器到评估界面
   - 如果没有自动打开，请手动访问: http://127.0.0.1:5000

4. 在Web界面中进行视频质量评估

📝 使用说明
--------
1. 程序启动后会显示当前评估进度
2. 点击"开始评估"或"继续评估"开始比较视频
3. 观看视频并选择质量最好的一个
4. 程序会自动进行淘汰赛，直到选出最佳视频
5. 最佳视频会自动复制到 Best_video 目录

⌨️ 快捷键
--------
- 数字键 1-4: 快速选择对应位置的视频
- 空格键: 播放/暂停所有视频
- Ctrl+C: 停止程序

📊 结果输出
--------
- Best_video/ 目录: 包含每个前缀的最佳视频
- evaluation_state.json: 当前评估状态
- evaluation_log.json: 详细评估日志
- 可通过Web界面导出完整报告

🔧 故障排除
--------
1. 程序无法启动
   - 检查是否有杀毒软件阻止
   - 确保有足够的磁盘空间
   - 尝试以管理员身份运行

2. 缺少必要文件
   - 确保 drone_prompts.csv 存在
   - 确保 dataset_drone_video 目录存在且包含 .mp4 视频文件
   - video_prefix_analysis.txt 是可选的，程序会自动扫描视频目录

3. 视频无法播放
   - 检查视频文件格式（支持 .mp4）
   - 确保浏览器支持HTML5视频
   - 尝试刷新页面

4. 浏览器无法访问
   - 检查防火墙设置
   - 确保端口5000未被占用
   - 尝试使用不同的浏览器

5. 评估状态丢失
   - 检查 evaluation_state.json 文件
   - 使用"重置评估"功能重新开始
   - 程序会自动从 Best_video 目录恢复进度

📞 技术支持
--------
如果遇到问题，请检查：
1. 控制台窗口中的错误信息
2. evaluation_log.json 中的详细日志
3. 确保所有必需文件完整

💡 提示
--------
- 建议定期备份 evaluation_state.json 文件
- 可以随时停止程序，下次启动会自动恢复进度
- Best_video 目录中的文件可以安全移动或复制
- 程序支持中文路径和文件名

版本信息: 独立部署版 v1.0
更新日期: 2024年
