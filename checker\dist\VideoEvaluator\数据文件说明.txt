📋 数据文件配置说明
========================================

请将以下文件放置在此目录中（与 VideoEvaluator.exe 同级）：

✅ 必需文件：
1. drone_prompts.csv - 提示文件
2. dataset_drone_video/ - 包含所有 .mp4 视频文件的目录

📋 可选文件：
1. video_prefix_analysis.txt - 视频前缀分析文件（如果提供将优先使用，否则自动扫描）

📁 目录结构示例：
VideoEvaluator/
├── VideoEvaluator.exe          # 主程序
├── start_standalone.bat        # 启动脚本
├── README_STANDALONE.txt       # 使用说明
├── 数据文件说明.txt            # 本文件
├── drone_prompts.csv          # ← 请放置此文件（必需）
├── dataset_drone_video/        # ← 请放置此目录（必需）
│   ├── video1.mp4
│   ├── video2.mp4
│   └── ...
├── video_prefix_analysis.txt   # ← 可选文件
├── Best_video/                # 输出目录（自动创建）
├── evaluation_state.json     # 评估状态（运行时生成）
└── evaluation_log.json       # 评估日志（运行时生成）

🚀 启动方式：
1. 确保上述必需文件已正确放置
2. 双击 start_standalone.bat 启动程序
3. 或者直接双击 VideoEvaluator.exe

📝 注意事项：
- 视频文件必须是 .mp4 格式
- 程序会自动扫描视频目录并分析前缀分组
- 确保文件路径中没有特殊字符
- 程序会自动创建 Best_video 目录用于输出结果

❓ 如有问题，请参考 README_STANDALONE.txt 文件
