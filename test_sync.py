#!/usr/bin/env python3
"""
测试同步功能的脚本
"""
import sys
import os

# 添加checker目录到路径
sys.path.append('checker')
sys.path.append(os.path.join(os.path.dirname(__file__), 'checker'))

from checker.video_processor import VideoProcessor

def test_sync_functionality():
    """测试同步功能"""
    print("=== 测试视频评估工具的同步功能 ===\n")
    
    # 配置路径
    VIDEO_DIR = "dataset_drone_video"
    ANALYSIS_FILE = "video_prefix_analysis.txt"
    BEST_VIDEO_DIR = "Best_video"
    PROMPTS_FILE = "drone_prompts.csv"
    
    try:
        # 初始化VideoProcessor
        print("1. 初始化VideoProcessor...")
        processor = VideoProcessor(ANALYSIS_FILE, VIDEO_DIR, BEST_VIDEO_DIR, PROMPTS_FILE)
        print(f"   ✅ 成功加载 {len(processor.prefix_groups)} 个前缀组")
        print(f"   ✅ 成功加载 {len(processor.prompts)} 个提示信息")
        
        # 获取同步前状态
        print("\n2. 检查同步前状态...")
        sync_status_before = processor.get_sync_status()
        print(f"   📁 Best_video文件数: {sync_status_before['best_video_files_count']}")
        print(f"   📊 状态记录数: {sync_status_before['state_completed_count']}")
        print(f"   🔄 是否同步: {'✅ 是' if sync_status_before['is_synced'] else '❌ 否'}")
        
        # 执行同步
        print("\n3. 执行同步操作...")
        sync_result = processor.sync_with_best_video_dir()
        
        print(f"   📁 扫描文件: {sync_result['scanned_files']} 个")
        print(f"   ✅ 恢复前缀: {sync_result['found_completed']} 个")
        print(f"   🗑️  移除前缀: {sync_result['removed_missing']} 个")
        
        if sync_result['updated_prefixes']:
            print(f"   📝 更新的前缀: {sync_result['updated_prefixes'][:3]}..." if len(sync_result['updated_prefixes']) > 3 else f"   📝 更新的前缀: {sync_result['updated_prefixes']}")
        
        if sync_result['removed_prefixes']:
            print(f"   🔄 移除的前缀: {sync_result['removed_prefixes'][:3]}..." if len(sync_result['removed_prefixes']) > 3 else f"   🔄 移除的前缀: {sync_result['removed_prefixes']}")
        
        if sync_result['errors']:
            print(f"   ⚠️  错误: {len(sync_result['errors'])} 个")
            for error in sync_result['errors'][:3]:
                print(f"      - {error}")
        
        # 获取同步后状态
        print("\n4. 检查同步后状态...")
        sync_status_after = processor.get_sync_status()
        status = processor.get_status()
        
        print(f"   📁 Best_video文件数: {sync_status_after['best_video_files_count']}")
        print(f"   📊 状态记录数: {sync_status_after['state_completed_count']}")
        print(f"   🔄 是否同步: {'✅ 是' if sync_status_after['is_synced'] else '❌ 否'}")
        print(f"   📈 总体进度: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
        
        # 测试Best_video目录内容
        print("\n5. Best_video目录内容:")
        if os.path.exists(BEST_VIDEO_DIR):
            best_videos = [f for f in os.listdir(BEST_VIDEO_DIR) if f.endswith('.mp4')]
            print(f"   📁 找到 {len(best_videos)} 个最佳视频文件")
            for video in best_videos[:5]:  # 显示前5个
                prefix = video.replace('_BEST.mp4', '')
                print(f"      - {video} (前缀: {prefix})")
            if len(best_videos) > 5:
                print(f"      ... 还有 {len(best_videos) - 5} 个文件")
        else:
            print("   ❌ Best_video目录不存在")
        
        print("\n=== 同步功能测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sync_functionality()
    sys.exit(0 if success else 1)
