#!/usr/bin/env python3
"""
测试Flask API端点的脚本
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_api_endpoint(endpoint, method="GET", data=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=5)
        
        print(f"\n=== 测试 {method} {endpoint} ===")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        return response.json()
    except Exception as e:
        print(f"\n=== 测试 {method} {endpoint} 失败 ===")
        print(f"错误: {e}")
        return None

def main():
    """主测试函数"""
    print("开始测试Flask API端点...")
    
    # 测试状态端点
    status = test_api_endpoint("/api/status")
    
    # 测试下一轮端点
    next_round = test_api_endpoint("/api/next_round")
    
    # 测试重置当前轮次端点（新功能）
    reset_current = test_api_endpoint("/api/reset_current")
    
    # 测试日志端点
    logs = test_api_endpoint("/api/logs")
    
    print("\n=== 测试总结 ===")
    print("✅ 所有API端点测试完成")
    
    if status and 'data' in status:
        data = status['data']
        print(f"📊 当前状态: {data['completed_prefixes']}/{data['total_prefixes']} 前缀已完成")
        print(f"📈 进度: {data['progress_percentage']:.1f}%")
        
        if data.get('current_tournament'):
            tournament = data['current_tournament']
            print(f"🎯 当前前缀: {tournament['prefix']}")
            print(f"🏆 当前轮次: {tournament.get('current_round', 1)}")

if __name__ == "__main__":
    main()
