/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 15px;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    text-align: center;
    color: #2c3e50;
    font-size: 2.2em;
    margin-bottom: 15px;
    font-weight: 300;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 500;
    color: #555;
    min-width: 200px;
    text-align: right;
}

/* 状态面板 */
.status-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.current-info h3 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.3em;
}

.current-info p {
    color: #666;
    font-size: 1.1em;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background: #3498db;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.control-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.skip-btn {
    background: #f39c12;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.skip-btn:hover {
    background: #e67e22;
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.reset-btn {
    background: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.reset-btn:hover {
    background: #c0392b;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.sync-btn {
    background: #9b59b6;
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.sync-btn:hover {
    background: #8e44ad;
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

/* 提示面板 */
.prompt-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #3498db;
}

.prompt-panel h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.prompt-panel p {
    color: #555;
    font-size: 1.1em;
    line-height: 1.6;
    font-style: italic;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #3498db;
    margin: 0;
}

/* 视频网格 */
.video-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
    justify-content: center;
}

/* 动态网格布局 */
.video-grid.grid-1 {
    grid-template-columns: 1fr;
    max-width: 700px;
    margin: 0 auto 20px;
}

.video-grid.grid-2 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 1200px;
    margin: 0 auto 20px;
}

.video-grid.grid-3 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1400px;
    margin: 0 auto 20px;
}

.video-grid.grid-4 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 1400px;
    margin: 0 auto 20px;
}

.video-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.video-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.video-item.hidden {
    display: none !important;
}

.video-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.video-number {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(52, 152, 219, 0.9);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.video-item video {
    width: 100%;
    height: 320px;
    border-radius: 10px;
    background: #000;
    object-fit: contain;
}

.video-info {
    margin-top: 12px;
    text-align: center;
}

.video-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1.1em;
    word-break: break-all;
}

.select-btn {
    width: 100%;
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    background: #27ae60;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(39, 174, 96, 0.3);
}

.select-btn:hover {
    background: #229954;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
}

.select-btn:active {
    transform: translateY(0);
}

/* 完成面板 */
.completion-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.completion-panel h2 {
    color: #27ae60;
    font-size: 2.5em;
    margin-bottom: 20px;
}

.completion-panel p {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 25px;
}

.completion-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 25px 0;
}

.completion-stats p {
    margin: 10px 0;
    font-size: 1.1em;
}

/* 加载面板 */
.loading-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 60px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e0e0e0;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.modal-content p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1em;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.modal-buttons .control-btn {
    flex: 1;
    max-width: 120px;
}

/* 底部信息 */
footer {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.help-info h4 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1.2em;
}

.help-info ul {
    list-style: none;
    color: #666;
}

.help-info li {
    margin: 6px 0;
    padding-left: 18px;
    position: relative;
}

.help-info li:before {
    content: "•";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.help-info kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 0.9em;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2em;
    }

    .video-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .status-panel {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .controls {
        justify-content: center;
    }

    .progress-info {
        flex-direction: column;
        gap: 10px;
    }

    .progress-text {
        text-align: center;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .video-item video {
        height: 200px;
    }

    .control-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .modal-content {
        padding: 20px;
        margin: 20px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}