@echo off
chcp 65001 > nul
title 构建独立可执行程序

echo ========================================
echo 🔨 构建视频质量评估工具 - 独立版本
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller准备就绪

REM 检查Flask
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装Flask...
    pip install flask
    if errorlevel 1 (
        echo ❌ Flask安装失败
        pause
        exit /b 1
    )
)

echo ✅ Flask准备就绪

REM 检查必要文件
echo 📋 检查源文件...

if not exist "app_standalone.py" (
    echo ❌ 缺少文件: app_standalone.py
    pause
    exit /b 1
)

if not exist "video_processor_standalone.py" (
    echo ❌ 缺少文件: video_processor_standalone.py
    pause
    exit /b 1
)

if not exist "templates" (
    echo ❌ 缺少目录: templates
    pause
    exit /b 1
)

if not exist "static" (
    echo ❌ 缺少目录: static
    pause
    exit /b 1
)

echo ✅ 源文件检查通过

REM 清理旧的构建文件
echo 🧹 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM 开始构建
echo 🔨 开始构建可执行程序...
echo    这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller VideoEvaluator.spec

if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成

REM 复制额外文件到输出目录
echo 📁 复制额外文件...
copy "start_standalone.bat" "dist\VideoEvaluator\"
copy "README_STANDALONE.txt" "dist\VideoEvaluator\"

REM 创建示例数据文件说明
echo 📝 创建示例数据文件说明...
echo # 请将以下文件放置在此目录中: > "dist\VideoEvaluator\数据文件说明.txt"
echo # 1. video_prefix_analysis.txt - 视频前缀分析文件 >> "dist\VideoEvaluator\数据文件说明.txt"
echo # 2. drone_prompts.csv - 提示文件 >> "dist\VideoEvaluator\数据文件说明.txt"
echo # 3. dataset_drone_video/ - 包含所有视频文件的目录 >> "dist\VideoEvaluator\数据文件说明.txt"
echo # >> "dist\VideoEvaluator\数据文件说明.txt"
echo # 然后双击 start_standalone.bat 启动程序 >> "dist\VideoEvaluator\数据文件说明.txt"

echo.
echo ========================================
echo 🎉 构建成功完成！
echo ========================================
echo.
echo 📁 输出目录: dist\VideoEvaluator\
echo 📋 包含文件:
echo    - VideoEvaluator.exe (主程序)
echo    - _internal\ (依赖文件)
echo    - start_standalone.bat (启动脚本)
echo    - README_STANDALONE.txt (使用说明)
echo    - 数据文件说明.txt (数据文件说明)
echo.
echo 📝 下一步:
echo    1. 将数据文件复制到 dist\VideoEvaluator\ 目录
echo    2. 双击 start_standalone.bat 启动程序
echo    3. 或者将整个 VideoEvaluator 文件夹分发给用户
echo.

pause
