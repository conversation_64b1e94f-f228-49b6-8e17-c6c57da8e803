([('VideoEvaluator.exe',
   'E:\\NIPS\\temp_code\\coding\\checker\\build\\VideoEvaluator\\VideoEvaluator.exe',
   'EXECUTABLE'),
  ('python311.dll', 'C:\\Users\\<USER>\\anaconda3\\python311.dll', 'BINARY'),
  ('select.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\anaconda3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\anaconda3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Users\\<USER>\\anaconda3\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\Users\\<USER>\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('static\\script.js',
   'E:\\NIPS\\temp_code\\coding\\checker\\static\\script.js',
   'DATA'),
  ('static\\style.css',
   'E:\\NIPS\\temp_code\\coding\\checker\\static\\style.css',
   'DATA'),
  ('templates\\index.html',
   'E:\\NIPS\\temp_code\\coding\\checker\\templates\\index.html',
   'DATA'),
  ('base_library.zip',
   'E:\\NIPS\\temp_code\\coding\\checker\\build\\VideoEvaluator\\base_library.zip',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-8.6.1.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography-44.0.1.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA')],)
